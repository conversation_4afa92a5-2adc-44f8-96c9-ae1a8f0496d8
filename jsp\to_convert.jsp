<body onload="initForm();" onfocus="onFocusForm();" >
  <jsp:include page="header.jsp">
    <jsp:param name="PARAM" value="トレンド指数登録（AUT90065）" />
  </jsp:include>
  <html:form action="weeklyTrendShisuUpdate" method="post" >

    <fvo:checkbox style="display: none;" value="1" property='confirmed' />
    <fvo:text style="display: none;" property='pre_method_name' />
    <!-- ↓検索条件 -->
    <div id="term">
      <div style="display: table; width: 100%; text-align: left; border-left: solid 1px #c0c0c0;">
      <!-- 50%にするとブラウザをドラッグしてリサイズした時にレイアウトが崩れるので、49.99%にしています -->

        <!-- 検索条件.変動部 開始 -->
        <fvo:span property='term_detail_area1' >
          <table border="0" cellspacing="0" align="left" style="width: 100%; ">
            <tr>
              <td style="padding:0;">
                  <table class="bunrui" style="width:49.99%;">
                    <tr>
                      <th>抜出</th>
                      <td>
                        <fvo:select-one property='nukidashi_kb' ></fvo:select-one>
                      </td>
                    </tr>
                  </table>
                <fvo:span property='bunrui1_area' >
                  <table class="bunrui" style="width:49.99%;">
                    <tr>
                      <th><fvo:span property='bunrui1_title' ></fvo:span></th>
                      <td>
                        <fvo:text style="ime-mode: disabled;" property='bunrui1_cd'  onblur="bunruiOnblur(this, 1);" />
                        <fvo:text size="20" property='bunrui1_na' styleClass="readOnly" />
                        <fvo:button value="選 択"  onclick="popup_select();" property='bunrui1_button' styleClass="popupButton" />
                        <fvo:button value="クリア" onclick="popup_clear();" property='bunrui1_clear' styleClass="clearButton" />
                      </td>
                    </tr>
                  </table>
                </fvo:span>
                <fvo:span property='bunrui2_area' >
                  <table class="bunrui" style="width:49.99%;">
                    <tr>
                      <th><fvo:span property='bunrui2_title' ></fvo:span></th>
                      <td>
                        <fvo:text style="ime-mode: disabled;" property='bunrui2_cd'  onblur="bunruiOnblur(this, 2);" />
                        <fvo:text size="20" property='bunrui2_na' styleClass="readOnly" />
                        <fvo:button value="選 択"  onclick="popup_select();" property='bunrui2_button' styleClass="popupButton" />
                        <fvo:button value="クリア" onclick="popup_clear();" property='bunrui2_clear' styleClass="clearButton" />
                      </td>
                    </tr>
                  </table>
                </fvo:span>
                <fvo:span property='bunrui3_area' >
                  <table class="bunrui" style="width:49.99%;">
                    <tr>
                      <th><fvo:span property='bunrui3_title' ></fvo:span></th>
                      <td>
                        <fvo:text style="ime-mode: disabled;" property='bunrui3_cd'  onblur="bunruiOnblur(this, 3);" />
                        <fvo:text size="20" property='bunrui3_na' styleClass="readOnly" />
                        <fvo:button value="選 択"  onclick="popup_select();" property='bunrui3_button' styleClass="popupButton" />
                        <fvo:button value="クリア" onclick="popup_clear();" property='bunrui3_clear' styleClass="clearButton" />
                      </td>
                    </tr>
                  </table>
                </fvo:span>
                <fvo:span property='bunrui4_area' >
                  <table class="bunrui" style="width:49.99%;">
                    <tr>
                      <th><fvo:span property='bunrui4_title' ></fvo:span></th>
                      <td>
                         <fvo:text style="ime-mode: disabled;" property='bunrui4_cd'  onblur="bunruiOnblur(this, 4);" />
                        <fvo:text size="20" property='bunrui4_na' styleClass="readOnly" />
                        <fvo:button value="選 択"  onclick="popup_select();" property='bunrui4_button' styleClass="popupButton" />
                        <fvo:button value="クリア" onclick="popup_clear();" property='bunrui4_clear' styleClass="clearButton" />
                      </td>
                    </tr>
                  </table>
                </fvo:span>
                <fvo:span property='bunrui5_area' >
                  <table class="bunrui" style="width:49.99%;">
                    <tr>
                      <th><fvo:span property='bunrui5_title' ></fvo:span></th>
                      <td>
                        <fvo:text style="ime-mode: disabled;" property='bunrui5_cd'  onblur="bunruiOnblur(this, 5);" />
                        <fvo:text size="20" property='bunrui5_na' styleClass="readOnly" />
                        <fvo:button value="選 択"  onclick="popup_select();" property='bunrui5_button' styleClass="popupButton" />
                        <fvo:button value="クリア" onclick="popup_clear();" property='bunrui5_clear' styleClass="clearButton" />
                      </td>
                    </tr>
                  </table>
                </fvo:span>
              </td>
            </tr>
          </table>
        </fvo:span>
      </div>
    </div>
    <!-- ↑検索条件 -->
    <table width="100%">
      <tr>
        <td width="1%" align="center">
          <fvo:submit value="&emsp;検&emsp;索&emsp;" onclick="return doAction(this);" property='search_button' styleClass="controlButton" />
          <fvo:submit value="&emsp;登&emsp;録&emsp;" onclick="return doAction(this);" property='update_button' styleClass="controlButton" />
          <fvo:submit value="&emsp;CSV書出し&emsp;" onclick="return doAction(this);" property='csv_button' styleClass="controlButton" />
          <fvo:button value="&emsp;戻&emsp;る&emsp;" onclick="javascript:window.close();" property='exit_button' styleClass="controlButton" />
        </td>
      </tr>
    </table>
    <table width="100%">
      <tr>
        <td align="center" style="height: 24px;">
          <fvo:span property='message' ></fvo:span>
        </td>
      </tr>
    </table>
    <div align="center">
      <fvo:span property='result_area' >
        <table>
          <tr>
            <td style="text-align: left;">
              <table class="list">
                <thead>
                  <tr>
                     <th align="center" style="width:103px; height:55px;">
                      <fvo:span property='header_bunrui_title' ></fvo:span>
                    </th>
                    <th align="center" style="width:118px;">
                      <div style="border: none; border-bottom: solid 1px #909090; width: 100%; height: 20px; padding-top: 5px;">
                        <span>年／週</span>
                      </div>
                      <div style="border: none; width: 100%; height: 20px; padding-top: 5px;">
                        期間
                      </div>
                    </th>
                    <logic:iterate name="WeeklyTrendShisuUpdateForm" property='result_header_cols' id="result_header_cols" indexId="result_header_colsindex" type="jp.co.vinculumjapan.mdware.dailyorder.struts.form.rows.WeeklyTrendShisuUpdateFormResult_header_colsBean" >
					<th align="center" style="width:103px">
                      <div style="border: none; border-bottom: solid 1px #909090; width: 100%; height: 20px; padding-top: 5px;">
                        <fvo:span property='<%="result_header_cols[" + result_header_colsindex + "].week_no"%>' ></fvo:span>
                      </div>
                      <div style="border: none; width: 100%; height: 20px; padding-top: 5px;">
                        <fvo:span property='<%="result_header_cols[" + result_header_colsindex + "].week_start_date"%>' ></fvo:span>～<fvo:span property='<%="result_header_cols[" + result_header_colsindex + "].week_end_date"%>' ></fvo:span>
                      </div>
                    </th>
					</logic:iterate>
                      <th align="center" style="width:48px;">店舗<br>設定</th>
                    <th align="center" style="width:49px;">店舗数</th>
                  </tr>
                </thead>
              </table>
              <div id="scrollResult" class="scroll" align="center" style="height: 400px;">
                <table id="result" class="list" >
                  <logic:iterate name="WeeklyTrendShisuUpdateForm" property='result_bunrui_rows' id="result_bunrui_rows" indexId="result_bunrui_rowsindex" type="jp.co.vinculumjapan.mdware.dailyorder.struts.form.rows.WeeklyTrendShisuUpdateFormResult_bunrui_rowsBean" >
				<tr >
                    <td align="left" style="width:103px; height:51px;">
                      <fvo:span property='<%="result_bunrui_rows[" + result_bunrui_rowsindex + "].bunrui_cd"%>' ></fvo:span>
                      <br>
                      <fvo:span property='<%="result_bunrui_rows[" + result_bunrui_rowsindex + "].bunrui_na"%>' ></fvo:span>
                    </td>
                     <th align="left" style="width:118px">トレンド指数<br>調整倍率<br>調整後トレンド指数</th>
                    <logic:iterate name="result_bunrui_rows" property='result_trend_cols' id="result_trend_cols" indexId="result_trend_colsindex" type="jp.co.vinculumjapan.mdware.dailyorder.struts.form.rows.WeeklyTrendShisuUpdateFormResult_bunrui_rowsResult_trend_colsBean" >
					<td class="numeric" style="width:103px;">
                      <fvo:span property='<%="result_bunrui_rows[" + result_bunrui_rowsindex + "].result_trend_cols[" + result_trend_colsindex + "].jiseki_trend_idx"%>' ></fvo:span>
                      <br>
                      <fvo:text size="7" maxlength="5"  style="ime-mode: disabled; width:49px; height:16px; box-sizing:content-box; margin-right:1px;" onchange="doblur(this);" onblur="doblur(this);" property='<%="result_bunrui_rows[" + result_bunrui_rowsindex + "].result_trend_cols[" + result_trend_colsindex + "].trend_chosei_rt"%>' styleClass="numeric" />
                      <br>&nbsp;
                      <fvo:span property='<%="result_bunrui_rows[" + result_bunrui_rowsindex + "].result_trend_cols[" + result_trend_colsindex + "].yosoku_trend_idx"%>' ></fvo:span>
                      <fvo:span style="display : none;" property='<%="result_bunrui_rows[" + result_bunrui_rowsindex + "].result_trend_cols[" + result_trend_colsindex + "].week_no"%>' ></fvo:span>
                      <fvo:span style="display : none;" property='<%="result_bunrui_rows[" + result_bunrui_rowsindex + "].result_trend_cols[" + result_trend_colsindex + "].dummy_fg"%>' ></fvo:span>
                    </td>
					</logic:iterate>
                    <td align="center" style="width:48px;">
                        <fvo:submit property='<%="result_bunrui_rows[" + result_bunrui_rowsindex + "].setting"%>' value="設定" onclick="return doAction(this);" ></fvo:submit>
                    </td>
                    <td align="right" style="width:49px">
                        <fvo:span property='<%="result_bunrui_rows[" + result_bunrui_rowsindex + "].tenpo_count"%>' ></fvo:span>
                    </td>
                  </tr>
				</logic:iterate>
                </table>
              </div>
            </td>
          </tr>
        </table>
      </fvo:span>
    </div>

</html:form></body>