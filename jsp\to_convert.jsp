<body bgcolor="#FFFFFF" text="#000000" leftmargin="0" topmargin="0" marginwidth="0" marginheight="0" onload="initForm();" onfocus="onFocusForm();" >
	<!------ 子画面共通ヘッダー  START ------>
	<!------ 子画面共通ヘッダー  END ------>
	<html:form action="BetsuTanabanList" method="post" ><br>
	<div align="center" class="title">
		採用棚番一覧
	</div>
	<br><br>
		<!---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
		<table align="center" cellspacing="0" cellpadding="0" >
			<tr>
				<td>
					<div align="left" style="width:422px; height:60px;">
						<table valign="top" align="left" border="0" bgcolor="#FFFFFF" cellspacing="0" cellpadding="0" style="font-weight:bold;">
							<col style="width:60px;">
							<col style="width:340px;">
							<tr height="35" bgcolor="#FFFFFF" valign="top">
								<td>
									商品：
								</td>
								<td>
									<fvo:span property='itemcd' ></fvo:span><br>
									<fvo:span property='itemnm' ></fvo:span>
								</td>
							</tr>
						</table>
					</div>
				</td>
			</tr>
		</table>
		<table align="center" cellspacing="0" cellpadding="0" >
			<tr>
				<td>
					<table valign="top" align="left" border="0" bgcolor="#FFFFFF" cellspacing="1" cellpadding="0" style="font-weight:bold;">
						<col style="width:60px;">
						<col style="width:100px;">
						<col style="width:60px;">
						<col style="width:66px;">
						<col style="width:110px;">
						<tr height="22" bordercolor="#99FFFF" bgcolor="#FFFFFF">
							<td>
								部門
							</td>
							<td>
								棚番
							</td>
							<td>
								棚段
							</td>
							<td>
								棚並
							</td>
							<td>
								登録日付
							</td>
						</tr>
					</table>
				</td>
			</tr>
			<tr>
				<td>
					<div align="left" style="overflow:scroll; overflow-x:hidden; width:422px; height:160px;">
						<table valign="top" align="left" border="0" bgcolor="#FFFFFF" cellspacing="1" cellpadding="0" style="font-weight:bold;">
							<col style="width:60px;">
							<col style="width:100px;">
							<col style="width:60px;">
							<col style="width:66px;">
							<col style="width:110px;">
							<logic:iterate name="BetsuTanabanListForm" property='result_rows' id="result_rows" indexId="result_rowsindex" type="jp.co.vinculumjapan.mdware.tanaoroshi.struts.form.rows.BetsuTanabanListFormResult_rowsBean" >
							<tr height="18" bordercolor="#99FFFF" bgcolor="#FFFFFF">
								<td>
									<fvo:span property='<%="result_rows[" + result_rowsindex + "].dptcd"%>' ></fvo:span>
								</td>
								<td>
									<fvo:span property='<%="result_rows[" + result_rowsindex + "].tananb"%>' ></fvo:span>
								</td>
								<td>
									<fvo:span property='<%="result_rows[" + result_rowsindex + "].tanadannb"%>' ></fvo:span>
								</td>
								<td>
									<fvo:span property='<%="result_rows[" + result_rowsindex + "].seq"%>' ></fvo:span>
								</td>
								<td>
									<fvo:span property='<%="result_rows[" + result_rowsindex + "].date"%>' ></fvo:span>
								</td>
							</tr>
							</logic:iterate>
						</table>
					</div>
				</td>
			</tr>
		</table>
		<!---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------->
		<table width="100%" border="0" cellpadding="5" cellspacing="0">
			<tr>
				<td width="100%" height="20" align="center" valign="top">
					<fvo:button value="閉じる" onclick="javascript:window.close()" style="font-size:16px; font-weight:bold; width:80px; height:30px;" property='submit' styleClass="btn_eob" />
				</td>
			</tr>
		</table>
		<fvo:span property='message' ></fvo:span>

</html:form></body>