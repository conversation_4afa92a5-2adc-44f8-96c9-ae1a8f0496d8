<body bgcolor="#FFFFFF" text="#000000" leftmargin="0" topmargin="0" marginwidth="0" marginheight="0" onload="initForm();" onfocus="onFocusForm();">
	<!------ システム共通ヘッダー  START ------>
	<html:form action="TanaoroshiJisshiDataTorikomi" method="post"
		enctype="multipart/form-data">
		<table border="0" cellspacing="0" cellpadding="0" style="width: 100%">
			<tbody>
				<tr>
					<td>
						<jsp:include page="header.jsp">
							<jsp:param name="PARAM" value="棚卸業者実施データ取込（TNR04001）" />
						</jsp:include>
					</td>
				</tr>
				<tr>
					<!------ Body START ------>
					<td align="center" valign="top">
						<!------ 検索条件入力部  START ------>
						<div class="term">
							<table border="0" cellspacing="1" cellpadding="0" class="term">
								<tr>
									<th>*対象ファイル</th>
									<td nowrap>
										<fvo:file tabindex="-1" value="" property='term_target_file' />
									</td>
								</tr>
							</table>
							<table border="0" cellspacing="1" cellpadding="0" class="term">
								<tr>
									<th>取込方法</th>
									<td nowrap>
										<fvo:select-one property='term_torikomi_kb'/>
									</td>
								</tr>
							</table>
						</div>
						<!------ 検索条件入力部  E N D ------>
						<br />
						<!------ 処理ボタン表示部  START ------>
						<div class="term">
							<table class="term_btn_area">
								<tr>
									<td align="center">
										<fvo:submit value="登録" onclick="return doAction(this);" property='insert' styleClass="controlButton" />
										<fvo:button value="&emsp;閉じる&emsp;" onclick="window.close();" property='back' styleClass="controlButton" />
									</td>
								</tr>
							</table>
						</div>
						<!------ 処理ボタン表示部  E N D ------>
						<!------ メッセージ表示部  START ------>
						<div class="term">
							<table class="term_msg_area">
								<col width="5%">
								<col width="90%">
								<col width="5%">
								<tr>
									<td>&nbsp;</td>
									<td align="center"><fvo:span style="color:#FF0000" property='message'/></td>
									<td>&nbsp;</td>
								</tr>
							</table>
						</div>
						<!------ メッセージ表示部  E N D ------>
					</td>
					<!------ Body END ------>
				</tr>
			</tbody>
		</table>
	</html:form>
</body>