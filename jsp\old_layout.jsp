<body bgcolor="#FFFFFF" text="#000000" leftmargin="0" topmargin="0" marginwidth="0" marginheight="0"  onload="initForm();" onfocus="onFocusForm();" >

	<!------ システム共通ヘッダー  START ------>
	<html:form action="TanaoroshiJisshiDataTorikomi" method="post"  enctype="multipart/form-data" >
	<jsp:include page="header.jsp">
		<jsp:param name="PARAM" value="棚卸業者実施データ取込（TNR04001）" />
	</jsp:include>
	<!------ システム共通ヘッダー  END ------>

	<!------ システム共通メニューバー  START ------>
	<tr>
		<td colspan="2"></td>
	</tr>
	<!------ システム共通メニューバー  END ------>

	<tr>
		<td align="left" valign="top">
			<br />
			<!------ Body START ------>
			<div align="center">

				<!------ 検索条件入力部  START ------>
				<table border="0">
					<tr>
						<td align="left">
							<table border="0" cellspacing="1" cellpadding="0" class="kensaku" style="visibility:hidden">
								<tr>
									<th style="width: 130px"><span class="need">*</span>店舗</th>
									<td nowrap>
										<fvo:text property="tenpocd" maxlength='<%= ResorceUtil.getInstance().getPropertie("TENPO_LENGTH") %>' size="5" />
										<fvo:text property="tenponm"/>
										<fvo:button value="選択" onclick="selectTenpo()" property='tenposelect' styleClass="popupButton" />
										<fvo:button value="クリア" style="" onclick="clearJoken('tenpoCd', 'tenpoNm');" property='tenpoclear' styleClass="clearButton" />
									</td>
								</tr>
							</table>
							<table border="0" cellspacing="1" cellpadding="0" class="kensaku" style="visibility:hidden">
								<tr>
   									<th style="width: 130px;">業者</th>
   									<td nowrap>
										<fvo:select-one property='gyosha' >
										</fvo:select-one>
									</td>
								</tr>
							</table>
							<table border="0" cellspacing="1" cellpadding="0" class="kensaku">
								<tr>
									<th style="width: 130px;"><span class="need">*</span>対象ファイル</th>
									<td nowrap>
										<fvo:file style="width:320px;" tabindex="-1" value="" property='targetfiletanpin' />
									</td>
								</tr>
							</table>
							<table border="0" cellspacing="1" cellpadding="0" class="kensaku">
								<tr>
									<th style="width: 130px;">取込方法</th>
									<td nowrap>
										<fvo:select-one property='torikomikind' ></fvo:select-one>
									</td>
								</tr>
							</table>
						</td>
					</tr>
				</table>

				<!------ 検索条件入力部  E N D ------>

				<!------ 処理ボタン表示部  START ------>
				<table>
					<tr>
						<td>
							<fvo:submit style="width : 80px;" value="登録" onclick="return doAction(this);" property='insert' styleClass="controlButton" />
							<fvo:button style="width : 80px;" value="戻る" onclick="window.close();" property='back' styleClass="controlButton" />
						</td>
					</tr>
				</table>
				<!------ 処理ボタン表示部  E N D ------>

				<br />

				<!------ メッセージ表示部  START ------>
				<fvo:span style="color:#FF0000" property='message' ></fvo:span>
				<!------ メッセージ表示部  E N D ------>

				<br>

			</div>
		</td>
	</tr>
		<!------ Body END ------>
		<!---- システム共通フッター START ---->
		<!-- <jsp:include page="footer.jsp" /> -->
		<!---- システム共通フッター END ---->

	</html:form>
</body>