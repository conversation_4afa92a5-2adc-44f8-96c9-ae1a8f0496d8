// ToConvertAction.java - Action class for the converted JSP
package jp.co.vinculumjapan.mdware.tanaoroshi.struts.action;

import java.util.List;
import jp.co.vinculumjapan.fvo.component.*;
import jp.co.vinculumjapan.mdware.tanaoroshi.struts.form.ToConvertForm;

public class ToConvertAction {

    private ToConvertForm form;

    // Properties from ToConvertForm.java

    /**
     * online_dt を返します
     * @return online_dt
     */
    public Text getOnline_dt() {
        return form.getOnline_dt();
    }

    /**
     * online_dt を設定します
     * @param Text online_dt
     */
    public void setOnline_dt(Text online_dt) {
        form.setOnline_dt(online_dt);
    }

    /**
     * pre_method_name を返します
     * @return pre_method_name
     */
    public Text getPre_method_name() {
        return ((ToConvertForm)form).getPre_method_name();
    }

    /**
     * pre_method_name を設定します
     * @param Text pre_method_name
     */
    public void setPre_method_name(Text pre_method_name) {
        ((ToConvertForm)form).setPre_method_name(pre_method_name);
    }

    /**
     * term_itemcd を返します
     * @return term_itemcd
     */
    public Text getTerm_itemcd() {
        return ((ToConvertForm)form).getTerm_itemcd();
    }

    /**
     * term_itemcd を設定します
     * @param Text term_itemcd
     */
    public void setTerm_itemcd(Text term_itemcd) {
        ((ToConvertForm)form).setTerm_itemcd(term_itemcd);
    }

    /**
     * term_itemnm を返します
     * @return term_itemnm
     */
    public Text getTerm_itemnm() {
        return ((ToConvertForm)form).getTerm_itemnm();
    }

    /**
     * term_itemnm を設定します
     * @param Text term_itemnm
     */
    public void setTerm_itemnm(Text term_itemnm) {
        ((ToConvertForm)form).setTerm_itemnm(term_itemnm);
    }

    /**
     * term_dept_area を返します
     * @return term_dept_area
     */
    public Span getTerm_dept_area() {
        return ((ToConvertForm)form).getTerm_dept_area();
    }

    /**
     * term_dept_area を設定します
     * @param Span term_dept_area
     */
    public void setTerm_dept_area(Span term_dept_area) {
        ((ToConvertForm)form).setTerm_dept_area(term_dept_area);
    }

    /**
     * term_dptcd を返します
     * @return term_dptcd
     */
    public Text getTerm_dptcd() {
        return ((ToConvertForm)form).getTerm_dptcd();
    }

    /**
     * term_dptcd を設定します
     * @param Text term_dptcd
     */
    public void setTerm_dptcd(Text term_dptcd) {
        ((ToConvertForm)form).setTerm_dptcd(term_dptcd);
    }

    /**
     * term_dept_na を返します
     * @return term_dept_na
     */
    public Text getTerm_dept_na() {
        return ((ToConvertForm)form).getTerm_dept_na();
    }

    /**
     * term_dept_na を設定します
     * @param Text term_dept_na
     */
    public void setTerm_dept_na(Text term_dept_na) {
        ((ToConvertForm)form).setTerm_dept_na(term_dept_na);
    }

    /**
     * term_dept_sub_button を返します
     * @return term_dept_sub_button
     */
    public Button getTerm_dept_sub_button() {
        return ((ToConvertForm)form).getTerm_dept_sub_button();
    }

    /**
     * term_dept_sub_button を設定します
     * @param Button term_dept_sub_button
     */
    public void setTerm_dept_sub_button(Button term_dept_sub_button) {
        ((ToConvertForm)form).setTerm_dept_sub_button(term_dept_sub_button);
    }

    /**
     * term_dept_sub_clear を返します
     * @return term_dept_sub_clear
     */
    public Button getTerm_dept_sub_clear() {
        return ((ToConvertForm)form).getTerm_dept_sub_clear();
    }

    /**
     * term_dept_sub_clear を設定します
     * @param Button term_dept_sub_clear
     */
    public void setTerm_dept_sub_clear(Button term_dept_sub_clear) {
        ((ToConvertForm)form).setTerm_dept_sub_clear(term_dept_sub_clear);
    }

    /**
     * search を返します
     * @return search
     */
    public Submit getSearch() {
        return ((ToConvertForm)form).getSearch();
    }

    /**
     * search を設定します
     * @param Submit search
     */
    public void setSearch(Submit search) {
        ((ToConvertForm)form).setSearch(search);
    }

    /**
     * back を返します
     * @return back
     */
    public Button getBack() {
        return ((ToConvertForm)form).getBack();
    }

    /**
     * back を設定します
     * @param Button back
     */
    public void setBack(Button back) {
        ((ToConvertForm)form).setBack(back);
    }

    /**
     * close を返します
     * @return close
     */
    public Button getClose() {
        return ((ToConvertForm)form).getClose();
    }

    /**
     * close を設定します
     * @param Button close
     */
    public void setClose(Button close) {
        ((ToConvertForm)form).setClose(close);
    }
}