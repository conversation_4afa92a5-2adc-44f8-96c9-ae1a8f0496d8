// ToConvertAction.java - Action class for the converted JSP
import java.util.List;

public class ToConvertAction {
    
    private ToConvertForm form;

    // Properties from ToConvertForm.java

    /**
     * online_dt を返します
     * @return online_dt
     */
    public String getOnline_dt() {
        return form.getOnline_dt();
    }

    /**
     * online_dt を設定します
     * @param online_dt
     */
    public void setOnline_dt(String online_dt) {
        form.setOnline_dt(online_dt);
    }

    /**
     * pre_method_name を返します
     * @return pre_method_name
     */
    public String getPre_method_name() {
        return form.getPre_method_name();
    }

    /**
     * pre_method_name を設定します
     * @param pre_method_name
     */
    public void setPre_method_name(String pre_method_name) {
        form.setPre_method_name(pre_method_name);
    }

    /**
     * term_itemcd を返します
     * @return term_itemcd
     */
    public String getTerm_itemcd() {
        return form.getTerm_itemcd();
    }

    /**
     * term_itemcd を設定します
     * @param term_itemcd
     */
    public void setTerm_itemcd(String term_itemcd) {
        form.setTerm_itemcd(term_itemcd);
    }

    /**
     * term_itemnm を返します
     * @return term_itemnm
     */
    public String getTerm_itemnm() {
        return form.getTerm_itemnm();
    }

    /**
     * term_itemnm を設定します
     * @param term_itemnm
     */
    public void setTerm_itemnm(String term_itemnm) {
        form.setTerm_itemnm(term_itemnm);
    }

    /**
     * term_dept_area を返します
     * @return term_dept_area
     */
    public String getTerm_dept_area() {
        return form.getTerm_dept_area();
    }

    /**
     * term_dept_area を設定します
     * @param term_dept_area
     */
    public void setTerm_dept_area(String term_dept_area) {
        form.setTerm_dept_area(term_dept_area);
    }

    /**
     * term_dptcd を返します
     * @return term_dptcd
     */
    public String getTerm_dptcd() {
        return form.getTerm_dptcd();
    }

    /**
     * term_dptcd を設定します
     * @param term_dptcd
     */
    public void setTerm_dptcd(String term_dptcd) {
        form.setTerm_dptcd(term_dptcd);
    }

    /**
     * term_dept_na を返します
     * @return term_dept_na
     */
    public String getTerm_dept_na() {
        return form.getTerm_dept_na();
    }

    /**
     * term_dept_na を設定します
     * @param term_dept_na
     */
    public void setTerm_dept_na(String term_dept_na) {
        form.setTerm_dept_na(term_dept_na);
    }

    /**
     * term_dept_sub_button を返します
     * @return term_dept_sub_button
     */
    public String getTerm_dept_sub_button() {
        return form.getTerm_dept_sub_button();
    }

    /**
     * term_dept_sub_button を設定します
     * @param term_dept_sub_button
     */
    public void setTerm_dept_sub_button(String term_dept_sub_button) {
        form.setTerm_dept_sub_button(term_dept_sub_button);
    }

    /**
     * term_dept_sub_clear を返します
     * @return term_dept_sub_clear
     */
    public String getTerm_dept_sub_clear() {
        return form.getTerm_dept_sub_clear();
    }

    /**
     * term_dept_sub_clear を設定します
     * @param term_dept_sub_clear
     */
    public void setTerm_dept_sub_clear(String term_dept_sub_clear) {
        form.setTerm_dept_sub_clear(term_dept_sub_clear);
    }

    /**
     * search を返します
     * @return search
     */
    public String getSearch() {
        return form.getSearch();
    }

    /**
     * search を設定します
     * @param search
     */
    public void setSearch(String search) {
        form.setSearch(search);
    }

    /**
     * back を返します
     * @return back
     */
    public String getBack() {
        return form.getBack();
    }

    /**
     * back を設定します
     * @param back
     */
    public void setBack(String back) {
        form.setBack(back);
    }

    /**
     * close を返します
     * @return close
     */
    public String getClose() {
        return form.getClose();
    }

    /**
     * close を設定します
     * @param close
     */
    public void setClose(String close) {
        form.setClose(close);
    }

    /**
     * message を返します
     * @return message
     */
    public String getMessage() {
        return form.getMessage();
    }

    /**
     * message を設定します
     * @param message
     */
    public void setMessage(String message) {
        form.setMessage(message);
    }

    /**
     * term_result_area を返します
     * @return term_result_area
     */
    public String getTerm_result_area() {
        return form.getTerm_result_area();
    }

    /**
     * term_result_area を設定します
     * @param term_result_area
     */
    public void setTerm_result_area(String term_result_area) {
        form.setTerm_result_area(term_result_area);
    }

    /**
     * term_info_item_area を返します
     * @return term_info_item_area
     */
    public String getTerm_info_item_area() {
        return form.getTerm_info_item_area();
    }

    /**
     * term_info_item_area を設定します
     * @param term_info_item_area
     */
    public void setTerm_info_item_area(String term_info_item_area) {
        form.setTerm_info_item_area(term_info_item_area);
    }

    /**
     * term_item_cd を返します
     * @return term_item_cd
     */
    public String getTerm_item_cd() {
        return form.getTerm_item_cd();
    }

    /**
     * term_item_cd を設定します
     * @param term_item_cd
     */
    public void setTerm_item_cd(String term_item_cd) {
        form.setTerm_item_cd(term_item_cd);
    }

    /**
     * term_item_nm を返します
     * @return term_item_nm
     */
    public String getTerm_item_nm() {
        return form.getTerm_item_nm();
    }

    /**
     * term_item_nm を設定します
     * @param term_item_nm
     */
    public void setTerm_item_nm(String term_item_nm) {
        form.setTerm_item_nm(term_item_nm);
    }

    /**
     * list2_area を返します
     * @return list2_area
     */
    public String getList2_area() {
        return form.getList2_area();
    }

    /**
     * list2_area を設定します
     * @param list2_area
     */
    public void setList2_area(String list2_area) {
        form.setList2_area(list2_area);
    }

    /**
     * list2_dptcd を返します
     * @return list2_dptcd
     */
    public String getList2_dptcd() {
        return form.getList2_dptcd();
    }

    /**
     * list2_dptcd を設定します
     * @param list2_dptcd
     */
    public void setList2_dptcd(String list2_dptcd) {
        form.setList2_dptcd(list2_dptcd);
    }

    /**
     * list2_tananb を返します
     * @return list2_tananb
     */
    public String getList2_tananb() {
        return form.getList2_tananb();
    }

    /**
     * list2_tananb を設定します
     * @param list2_tananb
     */
    public void setList2_tananb(String list2_tananb) {
        form.setList2_tananb(list2_tananb);
    }
}
